<template>
  <div class="agenda-page">
    <div class="agenda-title">{{ agendaTitle }}</div>
    <div class="agenda-list">
      <div
        v-for="item in fileList"
        :key="item.annexesId"
        class="agenda-item"
      >
        <img :src="mapIcon(item.fileType)" alt="" class="file-icon">
        <a class="file-name" :href="item.url">{{ item.fileName }}</a>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  data() {
    return {
      agendaId: this.$route.query.id,
      agendaTitle: '',
      fileList: []
    }
  },
  created() {
    this.queryAgendaInfo()
  },
  methods: {
    // 图标渲染
    mapIcon(type) {
      const icon_doc = require('@/assets/images/icon_doc.png')
      const icon_ppt = require('@/assets/images/icon_ppt.png')
      const icon_pdf = require('@/assets/images/icon_pdf.png')
      const icon_excel = require('@/assets/images/icon_excel.png')
      const mapping = {
        xls: icon_excel,
        xlsx: icon_excel,
        doc: icon_doc,
        docx: icon_doc,
        ppt: icon_ppt,
        pptx: icon_ppt,
        pdf: icon_pdf
      }
      return mapping[type]
    },
    // 获取议程信息
    queryAgendaInfo() {
      request({
        url: `/meeting/agenda/anonymous/${this.agendaId}`,
        method: 'get'
      }).then(res => {
        const { data, code } = res
        if (code === 200) {
          this.agendaTitle = data.agendaName
          this.fileList = [...data.otherFiles]
          if (data.mainFile) {
            this.fileList.unshift(data.mainFile)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.agenda-page{
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f0f0f0;
  .agenda-title{
    flex: 0;
    font-size: 24px;
    padding: 15px;
    font-weight: bold;
  }
  .agenda-list{
    flex: 1;
    display: flex;
    flex-direction: column;
    .agenda-item{
      display: flex;
      align-items: flex-start;
      padding-left: 10px;
      padding: 10px;
      border-bottom: 1px solid #9c9c9c;
      .file-icon{
        width: 27px;
        height: 27px;
        margin-right: 10px;
      }
      .file-name{
        font-size: 22px;
      }
    }
  }
}
</style>
