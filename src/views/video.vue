<template>
  <div class="video-page">
    <video ref="video" id="video" class="video-box" autoplay muted></video>
  </div>
</template>

<script>
export default {
  name: "VideoPage",
  data() {
    return {
      sdk: null
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // Close PC when user replay.
      if (this.sdk) {
        this.sdk.close();
      }
      this.sdk = new SrsRtcPlayerAsync();

      // https://webrtc.org/getting-started/remote-streams
      this.$refs.video.srcObject = this.sdk.stream
      // Optional callback, SDK will add track to stream.
      // sdk.ontrack = function (event) { console.log('Got track', event); sdk.stream.addTrack(event.track); };

      // For example: webrtc://r.ossrs.net/live/livestream
      // var url = $("#txt_url").val();
      // const url = 'webrtc://*************/live/239';
      const url = decodeURIComponent(this.$route.query.url);

      this.sdk.play(url).then(function(session){
        console.log(`output->session`,session)
        // $('#sessionid').html(session.sessionid);
        // $('#simulator-drop').attr('href', session.simulator + '?drop=1&username=' + session.sessionid);
      }).catch(function (reason) {
        this.sdk.close();
        console.error(reason);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.video-page{
  .video-box{
    display: block;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
  }
}
</style>
