<template>
  <div class="agenda-container">
    <el-row :gutter="10" class="mb10">
      <el-col :span="24">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
        <!-- <el-button
          type="success"
          plain
          icon="el-icon-folder-checked"
          size="mini"
          @click="handleSaveAgenda"
        >保存</el-button> -->
        <!-- <div class="tips pt5" style="color: #f56c6c;font-weight: bold;">修改排序后，需要点击保存按钮</div> -->
      </el-col>
    </el-row>
    <el-table
      v-loading="loading"
      :data="agendaList"
    >
      <el-table-column
        label="排序"
        prop="sort"
        align="center"
        width="50"
      />
      <el-table-column
        label="议题名称"
        prop="agendaName"
        align="center"
      />
      <el-table-column
        label="主文件"
        align="left"
      >
        <template #default="scope">
          <template v-if="scope.row.mainFile">
            <el-link
              type="primary"
              :underline="false"
              :href="scope.row.mainFile.url"
              target="_blank"
            >{{ scope.row.mainFile.fileName }}</el-link>
            <br>
            <el-button
              size="mini"
              type="danger"
              plain
              class="mt10"
              @click="handleDeleteMainFile(scope.row.mainFile)"
            >删除</el-button>
          </template>
          <el-button
            v-else
            size="mini"
            type="primary"
            plain
            @click="handleOpenUpload(scope.row)"
          >上传</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="附件"
        align="center"
      >
        <template #default="scope">
          <span class="icon-click" @click="handleOpenOtherUpload(scope.row)">
            <i class="el-icon-link icon" />
            {{ scope.row.otherFiles && scope.row.otherFiles.length }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="议题人员"
        align="center"
      >
        <template #default="scope">
          <span class="icon-click" @click="handleOpenAgendaUsers(scope.row)">
            <i class="el-icon-user icon" />
            {{ scope.row.participantsList && scope.row.participantsList.length }}
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="会议内容"
        prop="agendaContext"
        align="center"
      >
        <template #default="scope">
          <el-input
            v-model="scope.row.agendaContext"
            type="textarea"
            rows="4"
            resize="none"
          />
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
      >
        <template #default="scope">
          <el-button
            v-if="scope.$index > 0"
            size="mini"
            type="text"
            icon="el-icon-top"
            @click="moveUp(scope.$index, scope.row)"
          >上移</el-button>
          <el-button
            v-if="scope.$index !== agendaList.length - 1"
            size="mini"
            type="text"
            icon="el-icon-bottom"
            @click="moveDown(scope.$index, scope.row)"
          >下移</el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
          >修改</el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-delete"
            @click="handleDeleteAgenda(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 新增议程对话框 -->
    <el-dialog
      :title="addDialog.title"
      :visible.sync="addDialog.open"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="agendaForm" :model="addDialog.form" label-width="80px">
        <el-form-item label="议题名称" prop="agendaName" :rules="{required: true, message: '请输入议题名称'}">
          <el-input v-model="addDialog.form.agendaName" placeholder="请输入议题名称" maxlength="100" />
        </el-form-item>
        <!-- <el-form-item label="会议内容" prop="agendaContext" :rules="{required: true, message: '请输入会议内容'}">
          <el-input
            v-model="addDialog.form.agendaContext"
            type="textarea"
            rows="4"
            placeholder="请输入会议内容"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdd">确 定</el-button>
        <el-button @click="addDialog.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 上传主文件对话框 -->
    <el-dialog
      :title="uploadMainfile.title"
      :visible.sync="uploadMainfile.open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-upload
        ref="upload"
        :limit="1"
        :accept="uploadMainfile.accept"
        :headers="uploadMainfile.headers"
        :action="uploadMainfile.url"
        :data="uploadMainfile.data"
        :disabled="uploadMainfile.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="uploadMainfile.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 上传附件弹窗 -->
    <el-dialog
      :title="uploadOtherFile.title"
      :visible.sync="uploadOtherFile.open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-upload
        ref="otherUpload"
        multiple
        :limit="10"
        :accept="uploadOtherFile.accept"
        :headers="uploadOtherFile.headers"
        :action="uploadOtherFile.url"
        :data="uploadOtherFile.data"
        :disabled="uploadOtherFile.isUploading"
        :on-progress="handleOtherFileUploadProgress"
        :on-success="handleOtherFileSuccess"
        :auto-upload="false"
        name="files"
        drag
        style="text-align: center;"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <el-row class="mt10 mb10 text-center">
        <el-col>
          <el-button
            type="primary"
            plain
            size="mini"
            @click="submitOtherFileForm"
          >上传</el-button>
        </el-col>
      </el-row>
      <el-table :data="uploadOtherFile.fileList" class="mt10">
        <el-table-column label="文件名称" prop="fileName">
          <template #default="scope">
            <el-link
              type="primary"
              :underline="false"
              :href="scope.row.url"
              target="_blank"
            >{{ scope.row.fileName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="文件类型" prop="fileType" />
        <el-table-column label="创建时间" prop="createTime" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              type="text"
              size="mini"
              icon="el-icon-delete"
              @click="hanldeDeleteOtherFile(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 议程人员弹窗 -->
    <el-dialog
      :title="userDialog.title"
      :visible.sync="userDialog.open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-tabs v-model="userDialog.activeName" type="card">
        <el-tab-pane label="议程人员" name="yes">
          <el-button
            type="danger"
            plain
            size="mini"
            class="mb10"
            :disabled="!userDialog.yesChosenUsers.length"
            @click="handleDeleteAgendaUsers"
          >删除</el-button>
          <el-table
            :data="userDialog.selectedUsers"
            @selection-change="yesSelectChange"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column label="姓名" prop="nickName" align="center" />
            <el-table-column label="部门" prop="deptName" align="center" />
            <el-table-column label="签到时间" prop="signTime" align="center" />
            <el-table-column label="电话" prop="phonenumber" align="center" />
            <el-table-column label="是否评分" align="center">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.hasScore"
                  active-color="#13ce66"
                  active-text="是"
                  inactive-text="否"
                  disabled
                />
              </template>
            </el-table-column>
            <el-table-column label="是否发短信" align="center">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.receiveMessage"
                  active-color="#13ce66"
                  active-text="是"
                  inactive-text="否"
                  disabled
                />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="选择议程人员" name="no">
          <el-button
            type="primary"
            plain
            size="mini"
            class="mb10"
            :disabled="!userDialog.noChosenUsers.length"
            @click="handleAddAgendaUsers"
          >添加</el-button>
          <el-table
            :data="userDialog.noSelectUsers"
            @selection-change="noSelectChange"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column label="姓名" prop="nickName" align="center" />
            <el-table-column label="部门" prop="deptName" align="center" />
            <el-table-column label="签到时间" prop="signTime" align="center" />
            <el-table-column label="电话" prop="phonenumber" align="center" />
            <el-table-column label="是否评分" align="center">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.hasScore"
                  active-color="#13ce66"
                  active-text="是"
                  inactive-text="否"
                />
              </template>
            </el-table-column>
            <el-table-column label="是否发短信" align="center">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.receiveMessage"
                  active-color="#13ce66"
                  active-text="是"
                  inactive-text="否"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMeetingAgenda,
  deleteAgendaFile,
  addAgenda,
  saveAgenda,
  listAgendaSelectedUsers,
  listAgendaNoSelectUsers,
  addAgendaUsers,
  deleteAgendaUsers,
  listAgendaOtherFile,
  deleteAgendaOtherFile,
  deleteAgenda,
  updateAgenda
} from '@/api/meeting/list'
import { getToken } from '@/utils/auth'
export default {
  name: 'MeetingDetailAgenda',
  data() {
    return {
      meetingId: this.$route.params.id,
      loading: false,
      agendaList: [],
      /* 新增议程弹窗 */
      addDialog: {
        open: false,
        title: '新增议程',
        form: {}
      },
      /* 上传主文件弹窗 */
      uploadMainfile: {
        open: false,
        title: '上传主文件',
        accept: '.xlsx, .xls, .pdf, .ppt, .pptx, .doc, .docx',
        isUploading: false,
        headers: { Authorization: 'Bearer ' + getToken() },
        url: process.env.VUE_APP_BASE_API + '/meeting/annexes/uploadMainFile',
        data: {
          agendaId: ''
        }
      },
      /* 上传附件弹窗 */
      uploadOtherFile: {
        open: false,
        title: '附件列表',
        accept: '.xlsx, .xls, .pdf, .ppt, .pptx, .doc, .docx',
        isUploading: false,
        headers: { Authorization: 'Bearer ' + getToken() },
        url: process.env.VUE_APP_BASE_API + '/meeting/annexes/uploadFile',
        fileList: [],
        data: {
          agendaId: ''
        }
      },
      userDialog: {
        open: false,
        title: '议程人员',
        activeName: 'yes',
        selectedUsers: [],
        noSelectUsers: [],
        yesChosenUsers: [],
        yesMultiple: false,
        noChosenUsers: [],
        noMultiple: false,
        agendaId: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /* 获取议程列表 */
    getList() {
      listMeetingAgenda({
        pageNum: 1,
        pageSize: 99,
        meetingId: this.meetingId
      }).then(res => {
        this.agendaList = res.rows
      })
    },
    /* 议程上移 */
    moveUp(index, row) {
      const agendaList = this.agendaList
      agendaList.splice(index, 1)
      agendaList.splice(index - 1, 0, row)
      this.agendaList = agendaList
      this.handleSaveAgenda()
    },
    /* 议程下移 */
    moveDown(index, row) {
      const agendaList = this.agendaList
      agendaList.splice(index, 1)
      agendaList.splice(index + 1, 0, row)
      this.agendaList = agendaList
      this.handleSaveAgenda()
    },
    /* 上传主文件弹窗 */
    handleOpenUpload(row) {
      this.uploadMainfile.data = {
        agendaId: row.agendaId
      }
      this.uploadMainfile.open = true
    },
    // 主文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.uploadMainfile.isUploading = true
    },
    // 主文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.uploadMainfile.open = false
      this.uploadMainfile.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传主文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    /** 删除主文件操作 */
    handleDeleteMainFile(row) {
      const ids = row.annexesId
      this.$modal.confirm('是否确认删除该主文件').then(function() {
        return deleteAgendaFile(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /* 新增议程弹窗 */
    handleAdd() {
      this.addDialog.open = true
      this.addDialog.title = '新增议程'
      this.addDialog.form = {
        agendaName: undefined
      }
    },
    /** 修改议程弹窗 */
    handleEdit(row) {
      this.addDialog.open = true
      this.addDialog.title = '修改议程'
      this.addDialog.form = {
        agendaId: row.agendaId,
        agendaName: row.agendaName
      }
    },
    /* 新增议程提交 */
    submitAdd() {
      this.$refs.agendaForm.validate(valid => {
        if (valid) {
          const params = {
            meetingId: this.meetingId,
            ...this.addDialog.form
          }
          if (params.agendaId) {
            updateAgenda([params]).then(() => {
              this.$modal.msgSuccess('修改成功')
              this.addDialog.open = false
              this.getList()
            })
          } else {
            addAgenda(params).then(() => {
              this.$modal.msgSuccess('新增成功')
              this.addDialog.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 查询议程的附件列表 */
    async queryAgendaOtherFileList(agendaId) {
      const params = {
        pageNum: 1,
        pageSize: 999,
        agendaId
      }
      const res = await listAgendaOtherFile(params)
      this.uploadOtherFile.fileList = res.rows
    },
    /* 上传附件弹窗 */
    async handleOpenOtherUpload(row) {
      this.uploadOtherFile.data = {
        agendaId: row.agendaId
      }
      await this.queryAgendaOtherFileList(row.agendaId)
      this.uploadOtherFile.open = true
    },
    // 附件上传中处理
    handleOtherFileUploadProgress(event, file, fileList) {
      this.uploadOtherFile.isUploading = true
    },
    // 附件上传成功处理
    handleOtherFileSuccess(response, file, fileList) {
      this.uploadOtherFile.isUploading = false
      this.$refs.otherUpload.clearFiles()
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
      this.queryAgendaOtherFileList(this.uploadOtherFile.data.agendaId)
    },
    // 提交上传附件
    submitOtherFileForm() {
      this.$refs.otherUpload.submit()
    },
    // 删除附件
    hanldeDeleteOtherFile(row) {
      this.$modal.confirm('是否确认删除该附件').then(() => {
        return deleteAgendaOtherFile(row.annexesId)
      }).then(() => {
        this.queryAgendaOtherFileList(this.uploadOtherFile.data.agendaId)
        this.$modal.msgSuccess('删除成功')
        this.getList()
      }).catch(() => {})
    },
    // 保存会议议程
    handleSaveAgenda() {
      const params = this.agendaList
      // const canSave = params.every(item => item.agendaContext)
      // if (!canSave) {
      //   return this.$modal.msgError('请输入会议内容')
      // }
      saveAgenda(params).then(() => {
        this.$modal.msgSuccess('保存排序成功')
        this.getList()
      })
    },
    /** 获取议程人员列表 */
    async queryAgendaUsers(agendaId) {
      const [selectedRes, noSelectRes] = await Promise.all([
        listAgendaSelectedUsers(agendaId),
        listAgendaNoSelectUsers(this.meetingId, agendaId)
      ])
      this.userDialog.selectedUsers = selectedRes.rows
      this.userDialog.noSelectUsers = noSelectRes.rows
    },
    /* 打议程人员弹窗 */
    async handleOpenAgendaUsers(row) {
      await this.queryAgendaUsers(row.agendaId)
      this.userDialog.agendaId = row.agendaId
      this.userDialog.open = true
    },
    /* 议程人员多选事件 */
    yesSelectChange(selection) {
      this.userDialog.yesChosenUsers = selection.map(item => item.userId)
    },
    /* 删除议程人员 */
    handleDeleteAgendaUsers() {
      const userIds = this.userDialog.yesChosenUsers
      deleteAgendaUsers(this.userDialog.agendaId, userIds).then(() => {
        this.$modal.msgSuccess('删除成功')
        this.queryAgendaUsers(this.userDialog.agendaId)
      })
    },
    /* 选择议程人员多选事件 */
    noSelectChange(selection) {
      this.userDialog.noChosenUsers = selection
    },
    /* 添加议程人员 */
    handleAddAgendaUsers() {
      const users = this.userDialog.noChosenUsers
      addAgendaUsers(this.userDialog.agendaId, users).then(() => {
        this.$modal.msgSuccess('添加成功')
        this.queryAgendaUsers(this.userDialog.agendaId)
        this.getList()
      })
    },
    // 删除会议议程
    handleDeleteAgenda(row) {
      this.$modal.confirm('是否确认删除该数据项？').then(function() {
        return deleteAgenda(row.agendaId)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
        this.getList()
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-click{
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  .icon{
    font-size: 22px;
    color: #1890ff;
    margin-right: 5px;
  }
}
</style>
