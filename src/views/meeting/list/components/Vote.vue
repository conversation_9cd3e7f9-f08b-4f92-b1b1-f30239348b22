<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      size="small"
      inline
      label-width="80px"
    >
      <el-form-item
        label="投票名称"
        prop="topic"
      >
        <el-input
          v-model="queryParams.topic"
          placeholder="请输入投票名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :disabled="!voteList.length"
          @click="handleExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="voteList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" />
      <el-table-column label="投票名称" prop="topic" />
      <el-table-column label="投票说明" prop="remark" />
      <el-table-column label="投票排序" prop="sort" />
      <el-table-column label="创建人" prop="createBy" />
      <el-table-column label="创建时间" prop="createTime" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-s-data"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
          >修改</el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 新增/修改弹窗 -->
    <el-dialog
      :title="editDialog.form.voteId ? '修改投票' : '新增投票'"
      :visible.sync="editDialog.open"
      width="800px"
    >
      <el-form
        ref="form"
        :model="editDialog.form"
        label-width="150px"
      >
        <el-form-item label="投票主题" prop="topic" :rules="{required: true, message: '请输入投票主题'}">
          <el-input v-model="editDialog.form.topic" placeholder="请输入投票主题" />
        </el-form-item>
        <el-form-item label="关联议程主题" prop="agendaId">
          <el-select
            v-model="editDialog.form.agendaId"
            class="w100"
            filterable
            clearable
          >
            <el-option
              v-for="item in editDialog.agendaList"
              :key="item.agendaId"
              :value="item.agendaId"
              :label="item.agendaName"
            />
          </el-select>
          <div class="tips">不选择议题，则所有参会人员均可投票；选择议题，则只有该议题内的人员可以投票。</div>
        </el-form-item>
        <el-form-item v-if="editDialog.form.agendaId" prop="contactUser" label="指定投票人员">
          <el-checkbox-group v-model="editDialog.form.contactUserList">
            <el-checkbox
              v-for="item in voteUserList"
              :key="item.userId"
              :label="item.userId"
            >{{ item.nickName }}</el-checkbox>
          </el-checkbox-group>
          <div class="tips">如果不指定投票人，则议题内所有人员可投票；如果指定投票人，则只有指定的人能投票。</div>
        </el-form-item>
        <el-form-item label="投票模式" prop="voteModel" :rules="{required: true, message: '请选择投票模式'}">
          <el-select
            v-model="editDialog.form.voteModel"
            class="w100"
          >
            <el-option value="赞成,反对,弃权" label="模式一" />
            <el-option value="赞成,反对" label="模式二" />
          </el-select>
          <div class="tips">”模式一“赞成，反对，弃权；”模式二“赞成，反对</div>
        </el-form-item>
        <!-- <el-form-item label="投票显示开关" prop="isActive" :rules="{required: true, message: '请选择投票显示开关'}">
          <el-select
            v-model="editDialog.form.isActive"
            class="w100"
          >
            <el-option value="是" />
            <el-option value="否" />
          </el-select>
          <div class="tips">“是”投票项为打开状态；“否”投票项未打开，需要主持人手动开启</div>
        </el-form-item> -->
        <el-form-item label="投票匿名开关" prop="anonymousVote" :rules="{required: true, message: '请选择投票匿名开关'}">
          <el-select
            v-model="editDialog.form.anonymousVote"
            class="w100"
          >
            <el-option value="是" />
            <el-option value="否" />
          </el-select>
          <div class="tips">“是”开启匿名投票；“否”开始实名投票</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="editDialog.form.remark"
            type="textarea"
            rows="4"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitVote">确 定</el-button>
        <el-button @click="editDialog.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 投票详情弹窗 -->
    <el-dialog
      title="投票详情"
      :visible.sync="detailDialog.open"
      width="800"
    >
      <el-table :data="detailDialog.detailList">
        <el-table-column label="选项内容" prop="voteContent" align="center" />
        <el-table-column label="已选" prop="size" align="center" />
        <el-table-column label="已投票人员姓名" prop="voteUser" align="center" />
        <el-table-column label="百分比" prop="percent" align="center" />
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="detailDialog.open = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listVote,
  addVote,
  updateVote,
  deleteVote,
  queryVote
} from '@/api/meeting/vote'
import { listMeetingAgenda } from '@/api/meeting/list'
export default {
  name: 'Vote',
  data() {
    return {
      meetingId: this.$route.params.id,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        topic: undefined
      },
      ids: [],
      // 非多个禁用
      multiple: true,
      loading: false,
      total: 0,
      voteList: [],
      editDialog: {
        open: false,
        agendaList: [],
        form: {}
      },
      detailDialog: {
        open: false,
        detailList: []
      }
    }
  },
  computed: {
    voteUserList() {
      const agenda = this.editDialog.agendaList?.find(item => item.agendaId === this.editDialog.form.agendaId)
      return agenda?.participantsList || []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      const params = {
        meetingId: this.meetingId,
        ...this.queryParams
      }
      listVote(params).then(res => {
        this.voteList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /* 重置新增/修改表单 */
    reset() {
      this.editDialog.form = {
        voteId: undefined,
        topic: undefined,
        agendaId: undefined,
        agendaList: [],
        contactUserList: [],
        voteModel: undefined,
        isActive: undefined,
        anonymousVote: undefined,
        remark: undefined
      }
      this.resetForm('form')
    },
    /** 查看投票详情 */
    handleDetail(row) {
      if (row.anonymousVote === '是') {
        return this.$modal.msgError('匿名投票不允许查看投票详情')
      }
      queryVote({
        voteId: row.voteId
      }).then(res => {
        this.detailDialog.open = true
        this.detailDialog.detailList = res.data
      })
    },
    handleAdd() {
      /* 查询议程列表 */
      const params = {
        pageNum: 1,
        pageSize: 99,
        meetingId: this.meetingId
      }
      listMeetingAgenda(params).then(res => {
        this.editDialog.agendaList = res.rows
        this.reset()
        this.editDialog.open = true
      })
    },
    handleEdit(row) {
      /* 查询议程列表 */
      const params = {
        pageNum: 1,
        pageSize: 99,
        meetingId: this.meetingId
      }
      listMeetingAgenda(params).then(res => {
        this.editDialog.agendaList = res.rows
        this.reset()
        this.editDialog.form = {
          voteId: row.voteId,
          topic: row.topic,
          agendaId: row.agendaId,
          contactUserList: row.contactUserList || [],
          voteModel: row.voteModel,
          isActive: row.isActive,
          anonymousVote: row.anonymousVote,
          remark: row.remark
        }
        this.editDialog.open = true
      })
    },
    /* 新增/修改投票保存 */
    submitVote() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const params = {
            meetingId: this.meetingId,
            ...this.editDialog.form
          }
          if (this.editDialog.form.voteId) {
            updateVote(params).then(() => {
              this.$modal.msgSuccess('修改成功')
              this.editDialog.open = false
              this.getList()
            })
          } else {
            addVote(params).then(() => {
              this.$modal.msgSuccess('新增成功')
              this.editDialog.open = false
              this.getList()
            })
          }
        }
      })
    },
    handleDelete(row) {
      const ids = row?.voteId || this.ids
      this.$modal.confirm('是否确认删除所选的数据项？').then(function() {
        return deleteVote(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.voteId)
      this.multiple = !selection.length
    },
    // 导出投票结果
    handleExport() {
      this.download('meeting/vote/export', {
        meetingId: this.meetingId
      }, `投票结果-${this.parseTime(Date.now(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
}
</script>
