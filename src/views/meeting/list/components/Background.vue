<template>
  <div class="back-container">
    <div class="back-list">
      <div v-for="(src, index) in imgList" :key="index" class="back-item">
        <el-image
          class="item-img"
          :src="src"
          fit="cover"
        />
        <i v-if="curImg === src" class="item-check el-icon-check" />
        <div class="item-ctrl">
          <el-button type="primary" size="mini" @click="updateImg(src)">设为背景</el-button>
        </div>
      </div>
      <i /><i /><i /><i /><i /><i /><i />
    </div>
  </div>
</template>

<script>
import { listBackground, updateBackground } from '@/api/meeting/background'
import { getMeeting } from '@/api/meeting/list'

export default {
  name: 'Background',
  data() {
    return {
      curImg: undefined,
      meetingId: this.$route.params.id,
      imgList: []
    }
  },
  created() {
    this.queryMeetingInfo()
    this.queryImgList()
  },
  methods: {
    // 获取当前会议的信息
    queryMeetingInfo() {
      getMeeting(this.meetingId).then(res => {
        this.curImg = res.data.meetingScreenPic
      })
    },
    // 获取所有背景图列表
    queryImgList() {
      listBackground().then(res => {
        this.imgList = res.data
      })
    },
    // 设置背景图
    updateImg(src) {
      const params = {
        meetingId: this.meetingId,
        url: src
      }
      updateBackground(params).then(res => {
        this.$modal.msgSuccess('设置背景图成功')
        this.queryMeetingInfo()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.back-container{
  .back-list{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-right: -10px;
    .back-item{
      width: 262px;
      height: 172px;
      position: relative;
      border: 1px solid #c8c8c8;
      overflow: hidden;
      margin: 15px 10px 0 0;
      &:hover{
        .item-ctrl{
          bottom: 0;
        }
      }
      .item-img{
        width: 100%;
        height: 100%;
      }
      .item-check{
        position: absolute;
        top: 0;
        right: 0;
        font-weight: bold;
        color: #fff;
        font-size: 30px;
        background-color: rgba(0,0,0,.5);
        border-radius: 0 0 0 5px;
        padding: 0 10px;
      }
      .item-ctrl{
        position: absolute;
        left: 0;
        right: 0;
        bottom: -40px;
        background-color: rgba(0,0,0,.5);
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: 0.2s linear;
      }
    }
    & > i{
      width: 262px;
      margin-right: 10px;
    }
  }
}
</style>
