<template>
  <div class="app-container">
    <el-card shadow="never" class="mb20">
      <template #header>
        <div style="display: flex;justify-content: space-between;">
          <span>评分规则</span>
          <template v-if="ruleEdit">
            <el-button type="default" size="mini" style="margin-left: auto;margin-right: 10px;" @click="ruleEdit = false">取消</el-button>
            <el-button type="primary" size="mini" @click="editRuleImg">保存</el-button>
          </template>
          <template v-else>
            <el-button type="primary" size="mini" @click="ruleEdit = true">编辑</el-button>
          </template>
        </div>
      </template>
      <p v-if="!ruleEdit && !ruleImg">暂无规则图片</p>
      <image-upload v-else v-model="ruleImg" :disabled="!ruleEdit" :limit="1" :is-show-tip="ruleEdit" :file-size="20"></image-upload>
    </el-card>
    <el-form
      ref="queryForm"
      :model="queryParams"
      size="small"
      inline
      label-width="80px"
    >
      <el-form-item
        label="议程名称"
        prop="agendaName"
      >
        <el-input
          v-model="queryParams.agendaName"
          placeholder="请输入投票名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :disabled="!agendaScoreList.length"
          @click="handleExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="agendaScoreList"
    >
      <el-table-column label="议程名称" prop="agendaName" align="center" sortable />
      <el-table-column label="项目得分" prop="projectScore" align="center" sortable>
        <template #default="scope">
          <span>{{scope.row.projectScore}}分</span>&nbsp;&nbsp;
          <el-button type="text" size="mini" @click="handleDetail(scope.row.projectScoreList)">明细</el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column label="客户经理得分" prop="preSaleScore" align="center" sortable>
        <template #default="scope">
          <span>{{scope.row.preSaleScore}}分</span>&nbsp;&nbsp;
          <el-button type="text" size="mini" @click="handleDetail(scope.row.preSaleScoreList)">明细</el-button>
        </template>
      </el-table-column>
      <el-table-column label="解决方案经理得分" prop="solutionScore" align="center" sortable>
        <template #default="scope">
          <span>{{scope.row.solutionScore}}分</span>&nbsp;&nbsp;
          <el-button type="text" size="mini" @click="handleDetail(scope.row.solutionScoreList)">明细</el-button>
        </template>
      </el-table-column>
      <el-table-column label="交付经理得分" prop="deliveryScore" align="center" sortable>
        <template #default="scope">
          <span>{{scope.row.deliveryScore}}分</span>&nbsp;&nbsp;
          <el-button type="text" size="mini" @click="handleDetail(scope.row.deliveryScoreList)">明细</el-button>
        </template>
      </el-table-column>
      <el-table-column label="总得分" prop="sumScore" align="center" sortable>
        <template #default="scope">
          <span>{{scope.row.sumScore}}分</span>
        </template>
      </el-table-column> -->
      <el-table-column label="已评人数/总人数" prop="numScoreText" align="center" />
    </el-table>
    <!-- 评分详情弹窗 -->
    <el-dialog
      title="评分详情"
      :visible.sync="detailDialog.open"
      width="800"
    >
      <el-table :data="detailDialog.detailList">
        <el-table-column label="评分人员" prop="nickName" align="center" />
        <el-table-column label="评分分值" prop="score" align="center">
          <template #default="scope">{{scope.row.isScored ? scope.row.score : '未评分'}}</template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="detailDialog.open = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getScoreRule,
  listScoreAgenda2,
  updateScoreRule,
} from '@/api/meeting/score'
export default {
  name: 'Score',
  data() {
    return {
      meetingId: this.$route.params.id,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        agendaName: undefined
      },
      ids: [],
      // 非多个禁用
      multiple: true,
      loading: false,
      total: 0,
      agendaScoreList: [],
      detailDialog: {
        open: false,
        detailList: []
      },
      ruleImg: '',
      ruleEdit: false
    }
  },
  created() {
    this.getRuleImg()
    this.getList()
  },
  methods: {
    // 获取规则图片
    getRuleImg() {
      getScoreRule(this.meetingId).then(res => {
        this.ruleImg = res.data
      })
    },
    // 修改规则图片
    editRuleImg() {
      const params = {
        meetingId: this.meetingId,
        url: this.ruleImg
      }
      updateScoreRule(params).then(res => {
        this.ruleEdit = false
        this.$message.success('修改成功')
      })
    },
    getList() {
      this.loading = true
      const params = {
        meetingId: this.meetingId,
        ...this.queryParams
      }
      listScoreAgenda2(params).then(res => {
        this.agendaScoreList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 查看投票详情 */
    handleDetail(list) {
      this.detailDialog.open = true
      this.detailDialog.detailList = list
    },
    // 导出评分结果
    handleExport() {
      this.download('meeting/agenda/score2/export', {
        meetingId: this.meetingId
      }, `评分结果-${this.parseTime(Date.now(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
}
</script>
