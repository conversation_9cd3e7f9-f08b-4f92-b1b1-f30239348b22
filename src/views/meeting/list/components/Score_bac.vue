<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      size="small"
      inline
      label-width="80px"
    >
      <el-form-item
        label="议程名称"
        prop="agendaName"
      >
        <el-input
          v-model="queryParams.agendaName"
          placeholder="请输入投票名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :disabled="!agendaScoreList.length"
          @click="handleExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="agendaScoreList"
    >
      <el-table-column label="议程名称" prop="agendaName" align="center" />
      <el-table-column label="最高分" prop="maxScore" align="center" />
      <el-table-column label="最低分" prop="minScore" align="center" />
      <el-table-column label="总分" prop="sumScore" align="center" />
      <el-table-column label="平均分" prop="avgScore" align="center" />
      <el-table-column label="已评人数/总人数" prop="numScoreText" align="center" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-s-data"
            @click="handleDetail(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 评分详情弹窗 -->
    <el-dialog
      title="评分详情"
      :visible.sync="detailDialog.open"
      width="800"
    >
      <el-table :data="detailDialog.detailList">
        <el-table-column label="评分人员" prop="nickName" align="center" />
        <el-table-column label="评分分值" prop="score" align="center">
          <template #default="scope">{{scope.row.isScored ? scope.row.score : '未评分'}}</template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="detailDialog.open = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listScoreAgenda,
} from '@/api/meeting/score'
export default {
  name: 'Score',
  data() {
    return {
      meetingId: this.$route.params.id,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        agendaName: undefined
      },
      ids: [],
      // 非多个禁用
      multiple: true,
      loading: false,
      total: 0,
      agendaScoreList: [],
      detailDialog: {
        open: false,
        detailList: []
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      const params = {
        meetingId: this.meetingId,
        ...this.queryParams
      }
      listScoreAgenda(params).then(res => {
        this.agendaScoreList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 查看投票详情 */
    handleDetail(row) {
      this.detailDialog.open = true
      this.detailDialog.detailList = row.scoreList
    },
    // 导出评分结果
    handleExport() {
      this.download('meeting/agenda/score/export', {
        meetingId: this.meetingId
      }, `评分结果-${this.parseTime(Date.now(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
}
</script>
