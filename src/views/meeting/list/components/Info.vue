<template>
  <div class="info-container">
    <el-row :gutter="10">
      <el-col v-if="meetingDetail.meetingStatus === 'UN'" :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload2"
          @click="handleUpdateStatus('ING')"
        >发布会议</el-button>
      </el-col>
      <el-col v-if="meetingDetail.meetingStatus === 'ING'" :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-close"
          @click="handleUpdateStatus('DONE')"
        >关闭会议</el-button>
      </el-col>
      <template v-if="meetingDetail.meetingStatus === 'DONE'">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-circle-check"
            @click="handleUpdateStatus('ING')"
          >开启会议</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-download"
            @click="handleDownloadPack"
          >一键打包</el-button>
        </el-col>
      </template>
    </el-row>
    <el-descriptions
      :column="1"
      :title="meetingDetail.title"
      class="mt20"
    >
      <el-descriptions-item label="会议时间">
        {{ meetingDetail.beginTime }} ~ {{ meetingDetail.endTime }}
      </el-descriptions-item>
      <el-descriptions-item label="会议主持人">
        {{ meetingDetail.hostName }}
      </el-descriptions-item>
      <el-descriptions-item label="会服人员">
        {{ meetingDetail.waiterName }}
      </el-descriptions-item>
      <el-descriptions-item label="会议室">
        {{ meetingDetail.roomName }}
      </el-descriptions-item>
      <el-descriptions-item label="会议类型">
        {{ meetingDetail.meetingType }}
      </el-descriptions-item>
      <el-descriptions-item label="参会人员">
        {{ meetingDetail.users && meetingDetail.users.join('，') }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { getMeeting, updateMeeting } from '@/api/meeting/list'
export default {
  name: 'MeetingDetailInfo',
  data() {
    return {
      meetingId: this.$route.params.id,
      meetingDetail: {}
    }
  },
  created() {
    this.queryMeetingDetail()
  },
  methods: {
    /* 获取会议信息 */
    queryMeetingDetail() {
      getMeeting(this.meetingId).then(res => {
        this.meetingDetail = res.data
      })
    },
    /** 修改会议状态 */
    handleUpdateStatus(status) {
      const params = {
        meetingId: this.meetingId,
        meetingStatus: status
      }
      this.$modal.confirm('是否执行此操作？').then(function() {
        return updateMeeting(params)
      }).then(() => {
        this.queryMeetingDetail()
        this.$bus.$emit('update-meeting-list')
        this.$modal.msgSuccess('状态修改成功')
      }).catch(() => {})
    },
    /** 一键打包下载文件 */
    handleDownloadPack() {
      this.download('/meeting/annexes/zip', {
        meetingId: this.meetingId
      }, `${this.meetingDetail.title}${this.parseTime(Date.now(), '{y}{m}{d}{h}{i}{s}')}.zip`)
    }
  }
}
</script>
