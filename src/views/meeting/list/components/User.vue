<template>
  <div class="user-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['meeting:list:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['meeting:list:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="queryMeetingUsers"></right-toolbar>-->
    </el-row>
    <el-table
      v-loading="loading"
      :data="userList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
      />
      <el-table-column
        label="姓名"
        prop="userName"
        align="center"
      />
      <el-table-column
        label="部门"
        prop="deptName"
        align="center"
      />
      <el-table-column
        label="签到时间"
        prop="signTime"
        align="center"
      />
      <el-table-column
        label="电话"
        prop="phone"
        align="center"
      />
      <el-table-column label="同屏权限" prop="isScreen" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isScreen"
            active-text="是"
            inactive-text="否"
            @change="handleChangeScreen(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
      >
        <template #default="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="queryMeetingUsers"
    />
    <!--  新增参会人员对话框  -->
    <el-dialog
      title="新增参会人员"
      :visible.sync="addVisible"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="searchForm"
        :model="searchParmas"
        inline
        label-width="80px"
      >
        <el-form-item label="姓名">
          <el-input
            v-model="searchParmas.userName"
            placeholder="请输入姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="部门">
          <el-input
            v-model="searchParmas.deptName"
            placeholder="请输入部门"
            clearable
          />
        </el-form-item>
      </el-form>
      <el-table
        :data="searchedNoMeetingUserList"
        height="400px"
        @selection-change="handleUserSelection"
      >
        <el-table-column type="selection" align="center" />
        <el-table-column label="姓名" prop="nickName" align="center" />
        <el-table-column label="部门" prop="nickName" align="center">
          <template #default="scope">
            {{ scope.row.dept && scope.row.dept.deptName }}
          </template>
        </el-table-column>
        <el-table-column label="电话" prop="phonenumber" align="center" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUserOfMeeting,
  listUserOfNoMeeting,
  addMeetingUser,
  deleteMeetingUser,
  updateMeetingUser
} from '@/api/meeting/list'
export default {
  name: 'MeetingDetailUser',
  data() {
    return {
      meetingId: this.$route.params.id,
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      userList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      noMeetingUserList: [],
      addVisible: false,
      noMeetingUserChosen: [],
      searchParmas: {
        userName: '',
        deptName: ''
      }
    }
  },
  computed: {
    // 新增弹窗里面的搜索
    searchedNoMeetingUserList() {
      let result = this.noMeetingUserList
      if (this.searchParmas.userName) {
        result = result.filter(user => {
          return user.nickName.includes(this.searchParmas.userName)
        })
      }
      if (this.searchParmas.deptName) {
        result = result.filter(user => {
          return user.dept?.deptName.includes(this.searchParmas.deptName)
        })
      }
      return result
    }
  },
  created() {
    this.queryMeetingUsers()
  },
  methods: {
    // 查询已参会人员
    queryMeetingUsers() {
      const params = Object.assign({}, this.queryParams, {
        meetingId: this.meetingId
      })
      listUserOfMeeting(params).then(res => {
        this.userList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    /* 重置表单数据 */
    resetForm() {
      this.form = {
        userIds: undefined
      }
    },
    /* 取消操作 */
    cancel() {
      this.addVisible = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /* 新增操作 */
    handleAdd() {
      /* 查询未参会人员 */
      listUserOfNoMeeting({
        meetingId: this.meetingId
      }).then(res => {
        this.noMeetingUserList = res.data
        this.resetForm()
        this.addVisible = true
      })
    },
    /* 新增参会人员选择 */
    handleUserSelection(selection) {
      this.noMeetingUserChosen = selection.map(item => item.userId)
    },
    /* 新增提交表单 */
    submitForm() {
      if (!this.noMeetingUserChosen.length) {
        return this.$modal.msgWarning('请选择参会人员')
      }
      const params = {
        meetingId: this.meetingId,
        userIds: this.noMeetingUserChosen
      }
      addMeetingUser(params).then(() => {
        this.$modal.msgSuccess('新增成功')
        this.addVisible = false
        this.queryMeetingUsers()
      })
    },
    /* 删除操作 */
    handleDelete(row) {
      const userIds = row.id || this.ids
      const userNames = []
      if (row.userName) {
        userNames.push(row.userName)
      } else {
        for (const id of this.ids) {
          const userName = this.userList.find(user => user.id === id)?.userName
          userNames.push(userName)
        }
      }
      this.$modal.confirm('是否确认删除姓名为"' + userNames + '"的数据项？').then(function() {
        return deleteMeetingUser(userIds)
      }).then(() => {
        this.queryMeetingUsers()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 切换同屏权限 */
    handleChangeScreen(row) {
      const params = {
        id: row.id,
        isScreen: row.isScreen
      }
      updateMeetingUser(params).then(res => {
        this.$modal.msgSuccess('修改成功')
      }).catch(() => {
        this.queryMeetingUsers()
      })
    }
  }
}
</script>
