<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane
        v-for="item in tabsArr"
        :key="item.name"
        :name="item.name"
        :label="item.label"
      >
        <component :is="item.component" v-if="item.name === activeName" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Info from './components/Info.vue'
import User from './components/User.vue'
import Agenda from './components/Agenda.vue'
import Vote from './components/Vote.vue'
import Score from './components/Score.vue'
import Background from './components/Background.vue'

export default {
  name: 'Detail',
  components: {
    Info,
    User,
    Agenda,
    Vote,
    Score,
    Background
  },
  data() {
    return {
      activeName: 'info',
      tabsArr: [{
        label: '会议信息',
        name: 'info',
        component: Info
      }, {
        label: '参会人员',
        name: 'user',
        component: User
      }, {
        label: '会议议程',
        name: 'agenda',
        component: Agenda
      }, {
        label: '会议投票',
        name: 'vote',
        component: Vote
      }, {
        label: '议程评分',
        name: 'score',
        component: Score
      }, {
        label: '会议背景',
        name: 'background',
        component: Background
      }]
    }
  }
}
</script>
