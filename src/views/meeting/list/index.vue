<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="80px"
    >
      <!--      <el-form-item label="开始时间" prop="beginTime">-->
      <!--        <el-date-picker-->
      <!--          clearable-->
      <!--          v-model="queryParams.beginTime"-->
      <!--          type="date"-->
      <!--          value-format="yyyy-MM-dd"-->
      <!--          placeholder="请选择开始时间"-->
      <!--        >-->
      <!--        </el-date-picker>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="结束时间" prop="endTime">-->
      <!--        <el-date-picker-->
      <!--          clearable-->
      <!--          v-model="queryParams.endTime"-->
      <!--          type="date"-->
      <!--          value-format="yyyy-MM-dd"-->
      <!--          placeholder="请选择结束时间"-->
      <!--        >-->
      <!--        </el-date-picker>-->
      <!--      </el-form-item>-->
      <el-form-item label="会议标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入会议标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会议状态" prop="meetingStatus">
        <el-select
          v-model="queryParams.meetingStatus"
          clearable
          placeholder="请选择会议状态"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.sys_meeting_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['meeting:list:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['meeting:list:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['meeting:list:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <!-- <right-toolbar :show-search.sync="showSearch" @queryTable="getList" /> -->
    </el-row>

    <el-table v-loading="loading" :data="meetingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="meetingId" />
      <el-table-column label="会议标题" align="center" prop="title">
        <template #default="scope">
          <router-link :to="`/meeting/detail/index/${scope.row.meetingId}`" style="color: #1890ff;">{{ scope.row.title }}</router-link>
        </template>
      </el-table-column>
      <el-table-column
        label="开始时间"
        align="center"
        prop="beginTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.beginTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会议室" align="center" prop="roomName" />
      <el-table-column label="主持人" align="center" prop="hostName" />
      <el-table-column label="会服人员" align="center" prop="waiterName" />
      <el-table-column label="会议类型" align="center" prop="meetingType" />
      <el-table-column label="会议状态" align="center" prop="meetingStatus">
        <template #default="scope">
          <dict-tag :options="dict.type.sys_meeting_status" :value="scope.row.meetingStatus" />
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="会议标语"
        align="center"
        prop="meetingScreenTitle"
        show-overflow-tooltip
        width="100px"
      />
      <el-table-column label="会议背景图" align="center" prop="meetingScreenPic" /> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="180px"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['meeting:list:detail']"
            size="mini"
            type="text"
            icon="el-icon-tickets"
            @click="$router.push(`/meeting/detail/index/${scope.row.meetingId}`)"
          >详情</el-button>
          <el-button
            v-hasPermi="['meeting:list:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['meeting:list:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会议主对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="auto"
      >
        <el-form-item label="开始时间" prop="beginTime" :rules="{required: true, message: '请选择开始时间'}">
          <el-date-picker
            v-model="form.beginTime"
            clearable
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-value="new Date()"
            placeholder="请选择开始时间"
            class="w100"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime" :rules="{required: true, message: '请选择结束时间'}">
          <el-date-picker
            v-model="form.endTime"
            clearable
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-value="new Date()"
            placeholder="请选择结束时间"
            class="w100"
          />
        </el-form-item>
        <el-form-item label="会议标题" prop="title" :rules="{required: true, message: '请输入会议标题'}">
          <el-input v-model="form.title" placeholder="请输入会议标题" />
        </el-form-item>
        <el-form-item label="部门" prop="deptId" :rules="{required: true, message: '请选择部门'}">
          <treeselect
            v-model="form.deptId"
            :options="deptOptions"
            :show-count="true"
            placeholder="请选择部门"
            :clearable="false"
          />
        </el-form-item>
        <el-form-item label="会议室" prop="roomId" :rules="{required: true, message: '请选择会议室'}">
          <el-select
            v-model="form.roomId"
            filterable
            placeholder="请选择会议室"
            class="w100"
          >
            <el-option
              v-for="item in meetingRooms"
              :key="item.roomId"
              :value="item.roomId"
              :label="item.roomName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主持人" prop="hostId" :rules="{required: true, message: '请选择主持人'}">
          <el-select
            v-model="form.hostId"
            filterable
            placeholder="请选择主持人"
            class="w100"
          >
            <el-option
              v-for="item in meetingUsers"
              :key="item.userId"
              :value="item.userId"
              :label="item.nickName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="会服人员" prop="waiterId" :rules="{required: true, message: '请选择会服人员'}">
          <el-select
            v-model="form.waiterId"
            filterable
            placeholder="请选择会服人员"
            class="w100"
          >
            <el-option
              v-for="item in meetingUsers"
              :key="item.userId"
              :value="item.userId"
              :label="item.nickName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="会服短信" prop="waiterSms" :rules="{required: true, message: '请选择会服短信'}">
          <el-radio-group v-model="form.waiterSms">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="会议类型" prop="meetingTypeId" :rules="{required: true, message: '请选择会议类型'}">
          <el-select
            v-model="form.meetingTypeId"
            filterable
            placeholder="请选择会议类型"
            class="w100"
          >
            <el-option
              v-for="item in meetingTypes"
              :key="item.meetingTypeId"
              :value="item.meetingTypeId"
              :label="item.meetingTypeName"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="会议标语" prop="meetingScreenTitle">
          <el-input v-model="form.meetingScreenTitle" placeholder="请输入会议标语" />
        </el-form-item>
        <el-form-item label="会议背景图" prop="meetingScreenPic">
          <el-input v-model="form.meetingScreenPic" placeholder="请输入会议背景图" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMeeting,
  getMeeting,
  delMeeting,
  addMeeting,
  updateMeeting,
  listMeetingUser,
  deptTreeSelect,
  listMeetingRoom,
  listMeetingType
} from '@/api/meeting/list'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'List',
  dicts: ['sys_meeting_status'],
  components: {
    Treeselect
  },
  data() {
    return {
      // 部门树选项
      deptOptions: undefined,
      meetingUsers: [],
      meetingRooms: [],
      meetingTypes: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会议主表格数据
      meetingList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        beginTime: null,
        endTime: null,
        title: null,
        deptId: null,
        roomId: null,
        hostId: null,
        meetingTypeId: null,
        meetingStatus: null,
        meetingScreenTitle: null,
        meetingScreenPic: null,
        meetingMinutes: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  created() {
    this.$bus.$on('update-meeting-list', this.getList)
    this.getDeptTree()
    this.getMeetingUser()
    this.getMeetingRoom()
    this.getMeetingType()
    this.getList()
  },
  methods: {
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data
      })
    },
    /** 查询参会人员 */
    getMeetingUser() {
      listMeetingUser().then(res => {
        this.meetingUsers = res.data
      })
    },
    /** 查询会议室列表 */
    getMeetingRoom() {
      listMeetingRoom().then(res => {
        this.meetingRooms = res.data
      })
    },
    /** 查询会议类型列表 */
    getMeetingType() {
      listMeetingType().then(res => {
        this.meetingTypes = res.data
      })
    },
    /** 查询会议主列表 */
    getList() {
      this.loading = true
      listMeeting(this.queryParams).then(response => {
        this.meetingList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        meetingId: null,
        beginTime: null,
        endTime: null,
        title: null,
        deptId: null,
        roomId: null,
        hostId: null,
        waiterId: null,
        waiterSms: 1,
        meetingTypeId: null,
        meetingStatus: null,
        meetingScreenTitle: null,
        meetingScreenPic: null,
        meetingMinutes: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        status: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.meetingId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    async handleAdd() {
      this.reset()
      await Promise.all([
        this.getMeetingRoom(),
        this.getMeetingType()
      ])
      this.open = true
      this.title = '添加会议'
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset()
      await Promise.all([
        this.getMeetingRoom(),
        this.getMeetingType()
      ])
      const meetingId = row.meetingId || this.ids
      getMeeting(meetingId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改会议'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.meetingId !== null) {
            updateMeeting(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addMeeting(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const meetingIds = row.meetingId || this.ids
      this.$modal.confirm('是否确认删除会议主编号为"' + meetingIds + '"的数据项？').then(function() {
        return delMeeting(meetingIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/meeting/export', {
        ...this.queryParams
      }, `meeting_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
