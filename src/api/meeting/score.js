import request from '@/utils/request'

// 查询投票列表
export function listScoreAgenda(query) {
  return request({
    url: '/meeting/agenda/score/list',
    method: 'get',
    params: query
  })
}
// 查询投票列表
export function listScoreAgenda2(query) {
  return request({
    url: '/meeting/agenda/score2/list',
    method: 'get',
    params: query
  })
}
// 查询规则图片
export function getScoreRule(meetingId) {
  return request({
    url: `/meeting/agenda/score2/score/rule/${meetingId}`,
    method: 'get'
  })
}
// 修改规则图片
export function updateScoreRule(data) {
  return request({
    url: '/meeting/agenda/score2/score/rule',
    method: 'put',
    data: data
  })
}
