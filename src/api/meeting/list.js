import request from '@/utils/request'

// 查询会议列表
export function listMeeting(query) {
  return request({
    url: '/meeting/list',
    method: 'get',
    params: query
  })
}
// 查询会议详细
export function getMeeting(meetingId) {
  return request({
    url: '/meeting/' + meetingId,
    method: 'get'
  })
}
// 新增会议
export function addMeeting(data) {
  return request({
    url: '/meeting',
    method: 'post',
    data: data
  })
}
// 修改会议
export function updateMeeting(data) {
  return request({
    url: '/meeting',
    method: 'put',
    data: data
  })
}
// 删除会议
export function delMeeting(meetingId) {
  return request({
    url: '/meeting/' + meetingId,
    method: 'delete'
  })
}
// 查询用户列表
export function listMeetingUser() {
  return request({
    url: '/system/user/meeting/list',
    method: 'get'
  })
}
// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: '/system/user/meeting//deptTree',
    method: 'get'
  })
}
// 查询会议室列表
export function listMeetingRoom() {
  return request({
    url: '/meeting/room/add/list',
    method: 'get'
  })
}
// 查询会议类型列表
export function listMeetingType() {
  return request({
    url: '/meeting/type/add/list',
    method: 'get'
  })
}
// 根据会议查询已参会人员列表
export function listUserOfMeeting(params) {
  return request({
    url: '/meeting/user/list',
    method: 'get',
    params
  })
}
// 根据会议查询未参会人员供选择
export function listUserOfNoMeeting(params) {
  return request({
    url: '/system/user/meeting/expect',
    method: 'get',
    params
  })
}
// 新增参会人员
export function addMeetingUser(data) {
  return request({
    url: '/meeting/user/addBatch',
    method: 'post',
    data
  })
}
// 删除参会人员
export function deleteMeetingUser(ids) {
  return request({
    url: `/meeting/user/${ids}`,
    method: 'delete'
  })
}
// 查询会议议程列表
export function listMeetingAgenda(params) {
  return request({
    url: '/meeting/agenda/list',
    method: 'get',
    params
  })
}
// 删除议程文件
export function deleteAgendaFile(ids) {
  return request({
    url: `/meeting/annexes/${ids}`,
    method: 'delete'
  })
}
// 新增会议议程
export function addAgenda(data) {
  return request({
    url: '/meeting/agenda',
    method: 'post',
    data
  })
}
// 修改会议议程
export function updateAgenda(data) {
  return request({
    url: '/meeting/agenda',
    method: 'put',
    data
  })
}
// 保存会议议程（主要是修改内容和排序）
export function saveAgenda(data) {
  return request({
    url: '/meeting/agenda',
    method: 'put',
    data
  })
}
// 议程已选择人员列表
export function listAgendaSelectedUsers(agendaId) {
  return request({
    url: `/meeting/agenda/participants/${agendaId}`,
    method: 'get'
  })
}
// 议程可选择人员列表
export function listAgendaNoSelectUsers(meetingId, agendaId) {
  return request({
    url: `/meeting/agenda/participants/${meetingId}/${agendaId}`,
    method: 'get'
  })
}
// 添加议程人员
export function addAgendaUsers(agendaId, data) {
  return request({
    url: `/meeting/agenda/participants/${agendaId}`,
    method: 'post',
    data
  })
}
// 删除议程人员
export function deleteAgendaUsers(agendaId, userIds) {
  return request({
    url: `/meeting/agenda/participants/${agendaId}/${userIds}`,
    method: 'delete'
  })
}
// 查询议程附件列表
export function listAgendaOtherFile(params) {
  return request({
    url: `/meeting/annexes/list`,
    method: 'get',
    params
  })
}
// 删除议程附件
export function deleteAgendaOtherFile(annexesIds) {
  return request({
    url: `/meeting/annexes/${annexesIds}`,
    method: 'delete'
  })
}
// 会议文件一键打包
export function packMeetingFile(params) {
  return request({
    url: `/meeting/annexes/zip`,
    method: 'get',
    params
  })
}
// 删除会议议程
export function deleteAgenda(agendaIds) {
  return request({
    url: `/meeting/agenda/${agendaIds}`,
    method: 'delete'
  })
}
// 修改人员同屏权限
export function updateMeetingUser(data) {
  return request({
    url: `/meeting/user`,
    method: 'put',
    data
  })
}
