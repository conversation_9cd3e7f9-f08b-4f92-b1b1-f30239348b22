import request from '@/utils/request'

// 查询投票列表
export function listVote(query) {
  return request({
    url: '/meeting/vote/list',
    method: 'get',
    params: query
  })
}
// 新增投票
export function addVote(data) {
  return request({
    url: `/meeting/vote`,
    method: 'post',
    data
  })
}
// 修改投票
export function updateVote(data) {
  return request({
    url: `/meeting/vote`,
    method: 'put',
    data
  })
}
// 删除投票
export function deleteVote(voteIds) {
  return request({
    url: `/meeting/vote/${voteIds}`,
    method: 'delete'
  })
}
// 查询投票详情
export function queryVote(params) {
  return request({
    url: `/meeting/vote/user/list`,
    method: 'get',
    params
  })
}
